-- 导入依赖模块
require("share")

-- 初始化
init(1)

-- 全局配置
__isnlog__ = false
UI_API_Key = "bPDOP4AkqUguZpyrAgtmTm0q"
UI_Secret_Key = "P4QdxoSBkw4K267BrgF2Sf76jY5SrM3d"
提示字体大小 = 8
提示颜色 = "0xffffff"
字体颜色 = "0x030303"

-- 创建UI界面的函数
function _UI()
  -- 获取屏幕尺寸
  local screenWidth, screenHeight = getScreenSize()
  h = screenHeight
  w = screenWidth

  -- 创建主UI窗口
  local uiConfig = {}
  uiConfig.titles = "只因你太美"
  uiConfig.okname = "Runing(うんてん)"
  uiConfig.cancelname = "Quit(しゅうりょう)"
  uiConfig.config = "budian.dat"
  uiConfig.timer = 80
  uiConfig.width = screenWidth * 1.75
  uiConfig.pagination = 1
  uiConfig.pagenumtype = "number"
  uiConfig.selpage = "2"
  uiConfig.titlesize = 14
  UINew(uiConfig)
  -- 创建分割线
  UILine("center", 1136, 5, "255,0,0")

  -- 创建标题标签
  UILabel("2025抓鬼", 15, "center", "160,32,240", -1)

  -- 创建必填提示标签
  UILabel("(必填)", 10, "left", "160,32,240", 80, 1)

  -- 创建抓鬼类型单选框
  UIRadio("rdo", "小鬼,大小鬼", "1", -1, 0, "", 1, 6)
  -- 长安旗选择
  UILabel(1, "长安旗：", 10, "left", "255,0,0", 80, 1)
  UICombo(1, "UI_qizi_长安", "红旗,白旗,黄旗,绿旗,蓝旗", "0", 160, 1, false)

  -- 朱紫旗选择
  UILabel(1, "朱紫旗：", 10, "left", "255,0,0", 80, 1)
  UICombo(1, "UI_qizi_朱紫", "白旗,红旗,黄旗,绿旗,蓝旗", "0", 160, 0, false)

  -- 傲来旗选择
  UILabel(1, "傲来旗：", 10, "left", "255,0,0", 80, 1)
  UICombo(1, "UI_qizi_傲来", "黄旗,红旗,白旗,绿旗,蓝旗", "0", 160, 1, false)

  -- 长寿旗选择
  UILabel(1, "长寿旗：", 10, "left", "255,0,0", 80, 1)
  UICombo(1, "UI_qizi_长寿", "绿旗,红旗,白旗,黄旗,蓝旗", "0", 160, 0, false)
  -- 回复设置说明
  UILabel(1, "低于设置的百分比执行回复", 10, "center", "255,0,0", -1, 0)

  -- 角色回复设置
  UILabel(1, "角色回复", 10, "left", "0,0,255", 124, 1)
  UICombo(1, "UI_贼王角色回复", "30%,50%,70%,90%", "3", 124, 1)

  -- 宠物回复设置
  UILabel(1, "宠物回复", 10, "left", "0,0,255", 124, 1)
  UICombo(1, "UI_贼王宠物回复", "30%,50%,70%,90%", "3", 124, 0)
  -- 无底洞队长提示
  UILabel("若队长为无底洞请打开下列开关", 10, "center", "160,32,240", -1)

  -- 战斗加血开关
  UILabel("战斗加血", 10, "left", "255,105,180", 80, 1)
  UISwitch("switch1", "off", "m", "left")

  -- 延迟设置
  UILabel("延迟", 10, "left", "0,0,255", 80, 1)
  UIEdit("延迟", "延迟", "0", 10, "left", "255,0,0", "default", screenWidth * 0.25, 1)

  -- 显示UI
  UIShow()
end

-- 调用UI创建函数
_UI()

-- 根据用户选择执行相应的抓鬼流程
if rdo == "小鬼" then
  -- 执行小鬼抓鬼流程
  __抓鬼.main()
elseif rdo == "大小鬼" then
  -- 执行大小鬼抓鬼流程
  __抓鬼.大小鬼流程()
end
